import os
from datetime import datetime
from pathlib import Path

class FolderManager:
    """文件夹管理工具类，提供创建带日期结构的文件夹功能"""
    
    @staticmethod
    def _create_folder(folder_name: str) -> str:
        """
        创建文件夹的核心方法
        
        Args:
            folder_name: 文件夹名称
        
        Returns:
            str: 创建的文件夹完整路径
        """
        base_path = os.getcwd()  # 使用当前工作目录作为基础路径
        full_path = os.path.join(base_path, folder_name)
        Path(full_path).mkdir(parents=True, exist_ok=True)
        return str(Path(full_path).absolute())
    
    @staticmethod
    def create_month_folder(year: int = None, month: int = None) -> str:
        """
        创建年月文件夹，格式为"年-月"。如果不指定年月，则使用当前年月。
        
        Args:
            year: 年份（可选，默认使用当前年份）
            month: 月份（可选，默认使用当前月份）
        
        Returns:
            str: 创建的文件夹完整路径
            
        Raises:
            ValueError: 如果年月格式不正确
        
        Example:
            >>> FolderManager.create_month_folder()  # 创建当前年月文件夹
            "/current/working/dir/2023-11"
            >>> FolderManager.create_month_folder(2023, 11)  # 创建指定年月文件夹
            "/current/working/dir/2023-11"
        """
        if year is None and month is None:
            # 使用当前年月
            current_date = datetime.now()
            folder_name = current_date.strftime("%Y-%m")
        else:
            # 使用指定年月
            if year is None or month is None:
                raise ValueError("Both year and month must be specified together")
            if not (1 <= month <= 12):
                raise ValueError("Month must be between 1 and 12")
            if not (1900 <= year <= 9999):
                raise ValueError("Year must be between 1900 and 9999")
            folder_name = f"{year:04d}-{month:02d}"
        
        return FolderManager._create_folder(folder_name)
    
    @staticmethod
    def get_db_file_path(year: int = None, month: int = None) -> str:
        """
        获取数据库文件的完整路径，基于年月文件夹
        
        Args:
            year: 年份（可选，默认使用当前年份）
            month: 月份（可选，默认使用当前月份）
            
        Returns:
            str: 数据库文件的完整路径
            
        Example:
            >>> FolderManager.get_db_file_path()  # 获取当前年月文件夹下的数据库文件路径
            "/current/working/dir/2023-11/third_party_orders.db"
            >>> FolderManager.get_db_file_path(2023, 11)  # 获取指定年月文件夹下的数据库文件路径
            "/current/working/dir/2023-11/third_party_orders.db"
        """
        folder_path = FolderManager.create_month_folder(year, month)
        return os.path.join(folder_path, "third_party_orders.db")