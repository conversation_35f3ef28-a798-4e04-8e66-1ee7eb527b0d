from abc import ABC, abstractmethod
import requests
from typing import List
from .models import OrderResult, OrderInfo
from .config import ERPConfig

class ERPSystem(ABC):
    @abstractmethod
    def query_order(self, order: OrderInfo) -> OrderResult:
        """查询单个订单的金额数据"""
        pass

    @abstractmethod
    def query_orders(self, orders: List[OrderInfo]) -> List[OrderResult]:
        """批量查询订单的金额数据"""
        pass

    @staticmethod
    def get_corp_by_order_no(order_nos: List[str]) -> dict:
        url = "https://callback.vinehoo.com/orders/orders/v3/salesreturn/getcorpbyorderno"
        headers = {"Content-Type": "application/json"}
        payload = [{"sub_order_no": order_no, "order_type": 3} for order_no in order_nos]
        response = requests.post(url, json=payload, headers=headers)
        response_data = response.json()
        if response_data["error_code"] == 0 and response_data["data"]:
            return {item["sub_order_no"]: item["corp"] for item in response_data["data"]}
        payload = [{"sub_order_no": order_no, "order_type": 2} for order_no in order_nos]
        response = requests.post(url, json=payload, headers=headers)
        response_data = response.json()
        if response_data["error_code"] == 0 and response_data["data"]:
            return {item["sub_order_no"]: item["corp"] for item in response_data["data"]}
        return {}
    
    @staticmethod
    def get_corp_mapping_by_vh_code() -> dict:
        """根据 vh_code 获取 corp 映射信息"""
        result = {}
        url = "https://callback.vinehoo.com/erp/erp/v3/saleOrder/corpMappingList"
        response = requests.get(url)
        response_data = response.json()
        if response_data.get("error_code") == 0 and response_data.get("data"):
            for corp_mapping in response_data["data"]:
                vh_code = corp_mapping["vh_code"]
                result[vh_code] = corp_mapping
            return result
        raise result
    
    @staticmethod
    def convert_order_to_order_result(order: OrderInfo) -> OrderResult:
        """将 Order 转为 OrderResult"""
        order_result = OrderResult(
            error_code=0,
            error_msg="",
            order_id=order.order_id,
            erp_system="",
            account_id="",
            amount="",
            bill_date="",
            status=""
        )
        return order_result


class TPlusSystem(ERPSystem):
    def __init__(self):
        self.config = ERPConfig.ERP_SYSTEMS["T+"]
        self.name = "T+"

    def query_order(self, order: OrderInfo) -> OrderResult:
        result = self.convert_order_to_order_result(order)
        url = f"https://callback.vinehoo.com/push-t-plus/pushtplus/v3/SaleOrder/getVoucher?sub_order_no={order.order_id}&account_no={order.vh_code}"
        response = requests.get(url)
        data = response.json()
        
        if data["error_code"] != 0:
            result.error_code = -1
            if not data["error_msg"]:
                data["error_msg"] = "请求发送失败"
            result.error_msg = data["error_msg"]
            return result
        
        # 检查返回数据的完整性
        if not data.get("data"):
            result.error_code = -1
            result.error_msg = "订单数据不完整或不存在"
            return result
        
        try:
            order_data = data["data"]
            amount = float(order_data["SaleOrderDetails"][0]["OrigTaxAmount"])
            status = order_data["VoucherState"]["Code"]
            dbilldate = order_data["VoucherDate"]
        except (KeyError, IndexError, ValueError) as e:
            result.error_code = -1
            result.error_msg = f"解析订单数据失败: {str(e)}"
            return result
        
        return OrderResult(
            error_code=0,
            error_msg="",
            order_id=order.order_id,
            erp_system=self.name,
            account_id=f"t_plus_{order.erp_code}",
            amount=amount,
            bill_date=dbilldate,
            status=status
        )
    
    def query_orders(self, orders: List[OrderInfo]) -> List[OrderResult]:
        result: List[OrderResult] = []
        for order in orders:
            res = self.convert_order_to_order_result(order)
            if order.vh_code == "":
                res.error_code = -1
                res.error_msg = "订单号不存在"
                result.append(res)
                continue
            
            result.append(self.query_order(order))

        return result

class U8CSystem(ERPSystem):
    def __init__(self):
        self.config = ERPConfig.ERP_SYSTEMS["U8C"]
        self.name = "U8C"

    def query_order(self, order: OrderInfo) -> OrderResult:
        result = self.convert_order_to_order_result(order)

        url = f"https://callback.vinehoo.com/erp/erp/v3/saleOrder/query?code={order.order_id}&corp={order.erp_code}"
        response = requests.get(url)
        data = response.json()
        if data["error_code"] != 0:
            result.error_code = -1
            if not data["error_msg"]:
                data["error_msg"] = "请求发送失败"
            result.error_msg = data["error_msg"]
            return result
        
        # 检查返回数据的完整性
        if not data.get("data") or not data["data"].get("datas") or not data["data"]["datas"]:
            result.error_code = -1
            result.error_msg = "订单数据不完整或不存在"
            return result

        try:
            order_data = data["data"]["datas"][0]["parentvo"]
            amount = float(order_data["nheadsummny"])
            status = order_data["fstatus"]
            dbilldate = order_data["dbilldate"]
        except (KeyError, IndexError, ValueError) as e:
            result.error_code = -1
            result.error_msg = f"解析订单数据失败: {str(e)}"
            return result
        
        return OrderResult(
            error_code=0,
            error_msg="",
            order_id=order.order_id,
            erp_system=self.name,
            account_id=f"u8c_{order.erp_code}",
            amount=amount,
            bill_date=dbilldate,
            status=status
        )
    
    def query_orders(self, orders: List[OrderInfo]) -> List[OrderResult]:
        result: List[OrderResult] = []
        for order in orders:
            res = self.convert_order_to_order_result(order)
            if order.vh_code == "":
                res.error_code = -1
                res.error_msg = "订单号不存在"
                result.append(res)
                continue

            result.append(self.query_order(order))

        return result
