from abc import ABC, abstractmethod
from db_manager.base_mysql import MySQLDB
from settlement_downloader.platform_shop_config import Shop
from datetime import datetime


class SettlementPlatform(ABC):
    """结算单下载平台的抽象基类"""

    @abstractmethod
    def run(self, shop: Shop, db: MySQLDB):
        """处理结算单"""
        pass

    @abstractmethod
    def platform_type(self) -> str:
        """返回平台类型"""
        pass
