from datetime import datetime

import shop_manager
from db_manager.base_mysql import MySQLDB
from settlement_downloader.base import SettlementPlatform
import requests
import logging
import pandas as pd
import io

from settlement_downloader.exceptions import PlatformRunError
from settlement_downloader.platform_shop_config import Shop
from settlement_downloader.utils import get_first_day_of_month_timestamp, datetime_to_timestamp, \
    get_last_day_of_month_timestamp, get_last_month_str

logger = logging.getLogger(__name__)


class Xiaohongshu(SettlementPlatform):

    def platform_type(self) -> str:
        return shop_manager.PlatformType.XIAOHONGSHU.value

    def run(self, shop: Shop, db: MySQLDB):
        # 处理上月数据
        month = get_last_month_str()
        systemTaskCt = db.count("vh_tripartite_settlement_task", where="shop_id = %s and month = %s and type = 1",
                                args=(shop.shop_id, month))
        if systemTaskCt == 0:
            self._month_settlement(shop, db, month)

        # 处理人工添加的
        tasks = db.find_all('vh_tripartite_settlement_task', where="shop_id = %s and type = 2 and status = 1",
                            args=(shop.shop_id,), )
        if len(tasks) > 0:
            for task in tasks:
                self._month_settlement(shop, db, task["month"], task["id"], task['is_delete'] == 1)

    def _month_settlement(self, shop: Shop, db: MySQLDB, month: str, task_id: int = 0, is_delete: bool = False) -> bool:
        data = []
        try:
            # 将数据库格式(%Y%m)转换为接口格式(%Y-%m)
            api_month = f"{month[:4]}-{month[4:]}"

            # 请求接口拿到下载地址
            url = 'https://callback.vinehoo.com/xiaohongshu-sync/xiaohongshu/v3/bill_details'
            post_data = {
                'shop_id': shop.shop_id,
                'month': api_month
            }

            resp = requests.post(url, post_data).json()
            if resp.get('error_code', -1) != 0:
                raise PlatformRunError(f"服务器错误：响应:{resp}")

            download_url = resp.get('data', {}).get('downloadUrl', '')
            if not download_url:
                raise PlatformRunError(f"账单还未生成：响应:{resp}")

            # 下载并处理Excel文件
            file_data = self._download_and_process_excel(download_url, shop)
            data.extend(file_data)

            if data:
                with db.transaction() as conn:
                    if task_id == 0:
                        db.insert("vh_tripartite_settlement_task",
                                  {"shop_id": shop.shop_id, "month": month, "type": 1, "status": 2,
                                   "complete_time": datetime.now()}, conn=conn)
                    else:
                        if is_delete:
                            dt = datetime.strptime(month, "%Y%m")
                            db.delete("vh_tripartite_settlement",
                                      "shop_id = %s and create_time >= %s and create_time <= %s",
                                      (shop.shop_id, get_first_day_of_month_timestamp(dt),
                                       get_last_day_of_month_timestamp(dt)), conn=conn, )
                        db.update("vh_tripartite_settlement_task",
                                  {"status": "2", "complete_time": datetime.now()},
                                  'id = %s', (task_id,),
                                  conn=conn)
                    db.batch_insert("vh_tripartite_settlement", data, conn=conn)
        except Exception as e:
            logger.error(f"处理失败: {str(e)}")
            return False
        return True

    def _download_and_process_excel(self, download_url: str, shop: Shop) -> list:
        """下载并处理Excel文件"""
        # 下载Excel文件
        excel_response = requests.get(download_url)
        if excel_response.status_code != 200:
            raise PlatformRunError(
                f"下载Excel文件失败: status_code={excel_response.status_code}")

        # 读取Excel文件内容
        try:
            # 读取所有sheet
            excel_file = pd.ExcelFile(io.BytesIO(excel_response.content))
            data = []
            review_time = int(datetime.now().timestamp())

            for sheet in ["订单销售", "订单退款"]:
                if sheet in excel_file.sheet_names:
                    sheet_df = pd.read_excel(excel_file, sheet_name=sheet)
                    # 处理每一行数据
                    for _, row in sheet_df.iterrows():
                        record = self._process_excel_row(row, sheet)

                        # 添加公共字段
                        record.update({
                            'platform': self.platform_type(),
                            'shop_id': shop.shop_id,
                            'shop_name': shop.shop_name,
                            'entity': shop.entity,
                            'status': 2,  # 全部数据都完整，直接审核通过
                            'review_name': '系统',
                            'review_time': review_time,
                            'wait_amount': record.get('amount'),
                        })

                        data.append(record)

            return data

        except Exception as e:
            raise PlatformRunError(f"处理Excel文件失败: {str(e)}")

    def _process_excel_row(self, row, sheet: str) -> dict:
        """处理Excel数据行"""
        business = "收入"
        if sheet == "订单退款":
            business = "退款"

        # 将 pandas Timestamp 转换为 Python datetime，然后转为时间戳
        settlement_time = pd.to_datetime(row['结算时间']).to_pydatetime()
        create_time = datetime_to_timestamp(settlement_time.strftime("%Y-%m-%d %H:%M:%S"))

        return {
            'main_order_no': str(row['订单号']).strip(),
            'sub_order_no': str(row['订单号']).strip(),
            'business': business,
            'amount': float(row['计佣基数']),
            'create_time': create_time,
        }
