from flask import Flask, request, render_template
from .orders_manager import Orders<PERSON>anager
from .settlement_manager import SettlementManager
from datetime import datetime

class WebServer:
    def __init__(self, orders_manager: OrdersManager, settlement_manager: SettlementManager, host: str = '0.0.0.0', port: int = 5001):
        self.orders_manager = orders_manager
        self.settlement_manager = settlement_manager
        self.app = Flask(__name__, template_folder='templates')  # 指定模板目录
        self.host = host
        self.port = port

        # 添加时间戳转换过滤器
        self.app.jinja_env.filters['datetime'] = self._datetime_filter

        self._setup_routes()

    def _datetime_filter(self, timestamp, format='%Y-%m-%d'):
        """将时间戳转换为指定格式的日期字符串"""
        if not timestamp:
            return ''
        try:
            return datetime.fromtimestamp(timestamp).strftime(format)
        except (ValueError, TypeError):
            return ''

    def _setup_routes(self):
        """设置路由"""
        @self.app.route('/db', methods=['GET'])
        def get_db_data():
            try:
                # 获取分页参数
                page = int(request.args.get('page', 1))
                page_size = int(request.args.get('page_size', 10))
                table_type = request.args.get('table_type', 'orders')

                # 获取查询参数
                filters = {}
                for field in ['order_id', 'platform_type', 'platform_name', 'sales_order_date', 'shipping_time', 'main_order_id']:
                    if field in request.args and request.args[field]:
                        filters[field] = request.args[field]

                # 根据表类型选择管理器
                manager = self.orders_manager if table_type == 'orders' else self.settlement_manager

                # 查询数据
                result = manager.query_data(page, page_size, filters)

                if 'error' in result:
                    return f"Error: {result['error']}", 500

                # 计算总页数
                total_pages = (result['total'] + page_size - 1) // page_size if result['total'] > 0 else 1

                # 渲染 HTML 模板
                return render_template(
                    'db.html',
                    data=result['data'],
                    page=page,
                    page_size=page_size,
                    total=result['total'],
                    total_pages=total_pages,
                    filters=filters,
                    table_type=table_type
                )
            except Exception as e:
                return f"Error: {str(e)}", 500

    def run(self):
        """启动Web服务"""
        self.app.run(host=self.host, port=self.port, debug=True)