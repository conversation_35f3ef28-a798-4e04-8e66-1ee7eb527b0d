from shop_manager.constants import PlatformName, PlatformType
from vinehoo_order_fetcher.order_fetcher import OrderFetcher
import os


def main():
    # 创建 OrderFetcher 实例
    fetcher = OrderFetcher()

    try:
        # 选择要查询的平台
        platform = PlatformName.DOUYIN_JIUYUN_FLAGSHIP
        platform_type = PlatformType.DOUYIN

        # 获取指定月份的订单
        print("\n=== 获取指定月份的订单 ===")
        current_year = 2025
        current_month = 4

        # monthly_orders = fetcher.get_orders_by_month(
        #     platform=platform,
        #     platform_type=platform_type,  # 新增platform_type参数
        #     year=current_year,
        #     month=current_month
        # )
        #
        # print(f"本月订单数量: {len(monthly_orders)}")
        # for order in monthly_orders:
        #     print(f"订单ID: {order.order_id}")
        #     print(f"支付状态: {order.payment_status}")
        #     print(f"支付时间: {order.payment_time}")
        #     print(f"发货时间: {order.shipping_time}")
        #     print("-" * 50)
        #
        # # 测试根据订单ID获取订单
        # print("\n=== 根据订单ID获取订单 ===")
        # # 单个订单ID查询
        # test_order_id = "110168708125504651"
        # single_order = fetcher.get_orders_by_ids(
        #     platform=platform,
        #     platform_type=platform_type,  # 新增platform_type参数
        #     order_ids=test_order_id
        # )
        # if single_order:
        #     print("单个订单查询结果:")
        #     print(f"订单ID: {single_order.order_id}")
        #     print(f"支付状态: {single_order.payment_status}")
        #     print(f"支付金额: {single_order.payment_amount}")
        #     print(f"支付时间: {single_order.payment_time}")
        #     print("-" * 50)
        #
        # # 批量订单ID查询
        # test_order_ids = ["110168727005237407", "110168708125504651"]
        # multiple_orders = fetcher.get_orders_by_ids(
        #     platform=platform,
        #     platform_type=platform_type,  # 新增platform_type参数
        #     order_ids=test_order_ids
        # )
        # print("批量订单查询结果:")
        # for order in multiple_orders:
        #     print(f"订单ID: {order.order_id}")
        #     print(f"支付状态: {order.payment_status}")
        #     print(f"支付金额: {order.payment_amount}")
        #     print(f"支付时间: {order.payment_time}")
        #     print("-" * 50)
        #
        # # 测试同步数据到MySQL
        # print("\n=== 测试同步数据到MySQL ===")
        #
        # 测试多个平台同步
        print("\n--- 测试多个平台同步 ---")
        try:
            platforms_to_sync = [
                PlatformType.XIAOHONGSHU,
            ]
            synced_count = fetcher.sync_sales_data_to_mysql(
                year=current_year,
                month=current_month,
                platforms=platforms_to_sync
            )
            print(f"多个平台同步完成，同步记录数: {synced_count}")
        except Exception as e:
            print(f"多个平台同步失败: {str(e)}")
            
        # 测试上传文件到OSS
        # print("\n=== 测试上传文件到OSS ===")
        # try:
        #     # 本地文件路径
        #     local_file_path = "小红书订单明细_2025-02.xlsx"
        #
        #     # 检查文件是否存在
        #     if not os.path.exists(local_file_path):
        #         print(f"警告: 文件 '{local_file_path}' 不存在，尝试在不同目录查找...")
        #
        #         # 尝试在其他可能的目录查找
        #         possible_dirs = ["./data", "../data", "./excel", "../excel", "./downloads", "../downloads"]
        #         found = False
        #
        #         for directory in possible_dirs:
        #             test_path = os.path.join(directory, "小红书订单明细_2025-02.xlsx")
        #             if os.path.exists(test_path):
        #                 local_file_path = test_path
        #                 found = True
        #                 print(f"找到文件: {local_file_path}")
        #                 break
        #
        #         if not found:
        #             print(f"错误: 无法找到文件 '小红书订单明细_2025-02.xlsx'")
        #             print("请确保文件存在或修改文件路径")
        #             return
        #
        #     # 上传文件到指定目录
        #     target_dir = "tripartite_order/sales_orders"
        #     file_url = fetcher.upload_file_to_oss(local_file_path, target_dir)
        #
        #     print(f"文件上传成功!")
        #     print(f"访问URL: {file_url}")
        #
        # except Exception as e:
        #     print(f"上传文件到OSS失败: {str(e)}")

    except Exception as e:
        print(f"发生错误: {str(e)}")


if __name__ == "__main__":
    main()
