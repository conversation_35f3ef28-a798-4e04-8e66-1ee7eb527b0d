import pymysql
from pymysql.cursors import DictCursor
from contextlib import contextmanager
from typing import Union, List, Dict, Any, Optional, Tuple
from queue import LifoQueue
import logging
import traceback


class MySQLDB:
    def __init__(self,
                 host: str = 'localhost',
                 port: int = 3306,
                 user: str = 'root',
                 password: str = '',
                 database: str = None,
                 charset: str = 'utf8mb4',
                 pool_size: int = 5):
        self._host = host
        self._port = port
        self._user = user
        self._password = password
        self._database = database
        self._charset = charset
        self._pool_size = pool_size
        self._connection_pool = LifoQueue(maxsize=pool_size)
        self._logger = logging.getLogger('MySQLDB')

        # 日志默认输出到控制台
        if not self._logger.handlers:
            handler = logging.StreamHandler()
            handler.setFormatter(logging.Formatter('%(asctime)s [%(levelname)s] %(message)s'))
            self._logger.addHandler(handler)
            self._logger.setLevel(logging.INFO)

    def _create_connection(self) -> pymysql.Connection:
        return pymysql.connect(
            host=self._host,
            port=self._port,
            user=self._user,
            password=self._password,
            database=self._database,
            charset=self._charset,
            cursorclass=DictCursor
        )

    @contextmanager
    def _get_connection(self, reuse: bool = True) -> pymysql.Connection:
        conn = None
        try:
            if self._pool_size > 0 and reuse:
                try:
                    conn = self._connection_pool.get_nowait()
                    try:
                        conn.ping(reconnect=True)
                    except Exception:
                        conn = self._create_connection()
                except Exception:
                    conn = self._create_connection()
            else:
                conn = self._create_connection()
            yield conn
        finally:
            if conn:
                if reuse and self._pool_size > 0 and not conn._closed:
                    try:
                        self._connection_pool.put_nowait(conn)
                    except Exception:
                        conn.close()
                else:
                    conn.close()

    @contextmanager
    def transaction(self) -> pymysql.Connection:
        with self._get_connection(reuse=False) as conn:
            try:
                yield conn
                conn.commit()
                self._logger.debug("Transaction committed")
            except Exception as e:
                conn.rollback()
                self._logger.error(f"Transaction rolled back: {str(e)}\n{traceback.format_exc()}")
                raise

    def execute(self,
                sql: str,
                args: Optional[Union[Tuple, List, Dict[str, Any]]] = None,
                conn: Optional[pymysql.Connection] = None) -> int:
        args = args or ()
        if conn:
            with conn.cursor() as cursor:
                affected_rows = cursor.execute(sql, args)
                self._logger.debug(f"Executed SQL: {sql}, Args: {args}, Affected: {affected_rows}")
                return affected_rows
        else:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    affected_rows = cursor.execute(sql, args)
                    conn.commit()
                    self._logger.debug(f"Executed SQL: {sql}, Args: {args}, Affected: {affected_rows}")
                    return affected_rows

    def query(self,
              sql: str,
              args: Optional[Union[Tuple, List, Dict[str, Any]]] = None,
              conn: Optional[pymysql.Connection] = None) -> List[Dict]:
        args = args or ()
        if conn:
            with conn.cursor() as cursor:
                cursor.execute(sql, args)
                return cursor.fetchall()
        else:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql, args)
                    return cursor.fetchall()

    def query_one(self,
                  sql: str,
                  args: Optional[Union[Tuple, List, Dict[str, Any]]] = None,
                  conn: Optional[pymysql.Connection] = None) -> Optional[Dict]:
        args = args or ()
        if conn:
            with conn.cursor() as cursor:
                cursor.execute(sql, args)
                return cursor.fetchone()
        else:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql, args)
                    return cursor.fetchone()

    def insert(self,
               table: str,
               data: Dict[str, Any],
               conn: Optional[pymysql.Connection] = None) -> int:
        assert table.isidentifier(), f"Invalid table name: {table}"
        columns = ', '.join(data.keys())
        placeholders = ', '.join(['%s'] * len(data))
        sql = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"
        values = tuple(data.values())

        if conn:
            with conn.cursor() as cursor:
                cursor.execute(sql, values)
                return cursor.lastrowid
        else:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql, values)
                    conn.commit()
                    return cursor.lastrowid

    def batch_insert(self,
                     table: str,
                     data_list: List[Dict],
                     batch_size: int = 1000,
                     conn: pymysql.Connection = None) -> int:
        """
        批量插入数据（自动事务控制，确保全插入或全失败）
        :param table: 表名
        :param data_list: 要插入的数据列表
        :param batch_size: 每批执行多少条
        :param conn: 可选的外部连接（建议使用事务）
        :return: 插入的总行数
        """
        if not data_list:
            return 0

        total_rows = 0
        columns = ', '.join(data_list[0].keys())
        placeholders = ', '.join(['%s'] * len(data_list[0]))
        sql = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"

        def execute_batch(batch_data, cur_conn):
            nonlocal total_rows
            values = [tuple(item.values()) for item in batch_data]
            with cur_conn.cursor() as cursor:
                affected_rows = cursor.executemany(sql, values)
                total_rows += affected_rows

        if conn:
            # 已经在事务中，由调用者控制提交和回滚
            for i in range(0, len(data_list), batch_size):
                batch = data_list[i:i + batch_size]
                execute_batch(batch, conn)
        else:
            # 自动开启事务，确保全成功或全失败
            with self.transaction() as new_conn:
                for i in range(0, len(data_list), batch_size):
                    batch = data_list[i:i + batch_size]
                    execute_batch(batch, new_conn)

        return total_rows

    def update(self,
               table: str,
               data: Dict[str, Any],
               where: str,
               where_args: Optional[Union[Tuple, List, Dict[str, Any]]] = None,
               conn: Optional[pymysql.Connection] = None) -> int:
        assert table.isidentifier(), f"Invalid table name: {table}"
        set_clause = ', '.join([f"{k}=%s" for k in data.keys()])
        sql = f"UPDATE {table} SET {set_clause} WHERE {where}"

        if isinstance(where_args, dict):
            where_args_tuple = tuple(where_args.values())
        else:
            where_args_tuple = tuple(where_args) if where_args else ()

        args = tuple(data.values()) + where_args_tuple
        return self.execute(sql, args, conn)

    def delete(self,
               table: str,
               where: str,
               where_args: Optional[Union[Tuple, List, Dict[str, Any]]] = None,
               conn: Optional[pymysql.Connection] = None) -> int:
        assert table.isidentifier(), f"Invalid table name: {table}"
        sql = f"DELETE FROM {table} WHERE {where}"

        if isinstance(where_args, dict):
            args = tuple(where_args.values())
        else:
            args = tuple(where_args) if where_args else ()

        return self.execute(sql, args, conn)

    def close(self):
        while not self._connection_pool.empty():
            try:
                conn = self._connection_pool.get_nowait()
                conn.close()
            except Exception as e:
                self._logger.error(f"Error closing connection: {str(e)}")

    def count(self,
              table: str,
              where: str = "",
              args: Union[Tuple, List, Dict] = None,
              conn: pymysql.Connection = None) -> int:
        """
        查询记录数
        :param table: 表名
        :param where: WHERE 子句 (不含WHERE)
        :param args: 参数
        :return: 满足条件的记录数
        """

        sql = f"SELECT COUNT(*) as total FROM {table}"
        if where:
            sql += f" WHERE {where}"

        result = self.query_one(sql, args, conn)
        return int(result['total']) if result else 0

    def find_one(self,
                 table: str,
                 where: str = "",
                 args: Union[Tuple, List, Dict] = None,
                 columns: List[str] = None,
                 conn: pymysql.Connection = None) -> Optional[Dict]:

        """
        查询单条记录
        :param table: 表名
        :param where: WHERE 子句 (不含WHERE)
        :param args: 参数
        :param columns: 查询字段列表，默认 *
        :return: 单条记录或 None
        """
        column_str = ', '.join(columns) if columns else '*'
        sql = f"SELECT {column_str} FROM {table}"
        if where:
            sql += f" WHERE {where}"
        sql += " LIMIT 1"
        return self.query_one(sql, args, conn)

    def exists(self,
               table: str,
               where: str,
               args: Union[Tuple, List, Dict] = None,
               conn: pymysql.Connection = None) -> bool:
        """
        判断是否存在符合条件的记录
        :return: True / False
        """
        sql = f"SELECT 1 FROM {table} WHERE {where} LIMIT 1"
        result = self.query_one(sql, args, conn)
        return result is not None

    def find_all(self,
                 table: str,
                 where: str = "",
                 args: Union[Tuple, List, Dict] = None,
                 columns: List[str] = None,
                 order_by: str = "",
                 limit: Optional[int] = None,
                 conn: pymysql.Connection = None) -> List[Dict]:
        """
        查询所有记录（非分页）
        :param columns: 字段列表
        :param where: WHERE 子句 (不含WHERE)
        :param order_by: 排序语句（如 id DESC）
        :param limit: 限制返回记录数
        """
        column_str = ', '.join(columns) if columns else '*'
        sql = f"SELECT {column_str} FROM {table}"
        if where:
            sql += f" WHERE {where}"
        if order_by:
            sql += f" ORDER BY {order_by}"
        if limit is not None:
            sql += f" LIMIT {limit}"

        return self.query(sql, args, conn)
