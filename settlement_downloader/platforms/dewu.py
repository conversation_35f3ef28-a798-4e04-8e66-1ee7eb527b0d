import warnings
from datetime import datetime, date
from typing import Dict, Any

from dateutil.relativedelta import relativedelta

import shop_manager
from db_manager import TableFields
from db_manager.base_db_manager import BaseDBManager
from settlement_downloader.base import SettlementPlatform
import requests
import logging
import pandas as pd
import io

from settlement_downloader.exceptions import PlatformRunError
from settlement_downloader.platform_shop_config import Shop

logger = logging.getLogger(__name__)


class DeWu(SettlementPlatform):

    def platform_type(self) -> str:
        return shop_manager.PlatformType.OTHER.value

    def run(self, shop: Shop, db: BaseDBManager, start_date: datetime, end_date: datetime) -> list[
        Dict[TableFields, Any]]:

        # last_day = self.get_last_week_last_day(end_date.year, end_date.month)
        # print(last_day)
        # exit()

        query = """
        SELECT COUNT(*) FROM settlements
        WHERE platform_id = ?
        AND settlement_time >= ?
        AND settlement_time <= ?
        """
        result = db.execute_query(
            query, (shop.shop_id, start_date, end_date))
        if result and result[0][0] > 0:
            logger.info(
                f"店铺 {shop.shop_name} 的 {start_date.strftime('%Y-%m')} 月数据已存在，跳过处理")
            return []

        data = []

        # 请求接口拿到下载地址
        url = 'https://callback.vinehoo.com/tp6-dewu-sync/dewu-orders-sync/v3/bill_details'
        post_data = {
            'shop_id': shop.shop_id,
            'startTime': start_date.strftime("%Y-%m-%d %H:%M:%S"),
            'endTime': end_date.strftime("%Y-%m-%d %H:%M:%S"),
        }

        resp = requests.post(url, post_data, timeout=30).json()
        if resp.get('error_code', -1) != 0:
            raise PlatformRunError(f"响应:{resp}")

        download_urls = resp.get('data', {}).get('download', [])

        # 忽略样式警告
        warnings.filterwarnings('ignore', category=UserWarning, message="Workbook contains no default style")

        # 读取Excel文件内容
        try:
            for download_url in download_urls:
                # 下载Excel文件
                excel_response = requests.get(download_url)
                if excel_response.status_code != 200:
                    raise PlatformRunError(
                        f"下载Excel文件失败: status_code={excel_response.status_code}")

                # 读取所有sheet
                excel_file = pd.ExcelFile(io.BytesIO(excel_response.content))
                for sheet in ["销售订单", "退货退款订单"]:
                    if sheet in excel_file.sheet_names:
                        sheet_df = pd.read_excel(excel_file, sheet_name=sheet, skiprows=3)
                        # 处理每一行数据
                        for _, row in sheet_df.iterrows():
                            settlement_type = "收入"
                            if sheet == "退货退款订单":
                                settlement_type = "退款"

                            data.append({
                                TableFields.ORDER_ID: str(row['订单号']),
                                TableFields.PLATFORM_ID: shop.shop_id,
                                TableFields.PLATFORM_NAME: shop.shop_name,
                                TableFields.PLATFORM_TYPE: self.platform_type(),
                                TableFields.SETTLEMENT_TIME: row['发货时间'],
                                TableFields.SETTLEMENT_AMOUNT: float(row['应结金额']),
                                TableFields.SETTLEMENT_TYPE: settlement_type,
                                TableFields.SETTLEMENT_CHANNEL: "",
                            })
        except Exception as e:
            raise PlatformRunError(f"处理Excel文件失败: {str(e)}")
        if len(data) > 0:
            if not db.insert_batch_data(data):
                raise PlatformRunError(f"保存数据失败")

        return data

    def get_last_week_last_day(self, year, month):
        # 下个月的第一天
        next_month = date(year, month, 1) + relativedelta(months=1)
        # 本月的最后一天
        last_day = next_month - relativedelta(days=1)

        # 找到最后一周的周日
        last_sunday = last_day - relativedelta(days=last_day.weekday() + 1)
        return last_sunday
