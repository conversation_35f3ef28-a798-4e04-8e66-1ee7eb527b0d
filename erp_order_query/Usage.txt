from erp_order_query import OrderQuery, Order

def main():
    # 创建订单查询实例
    query = OrderQuery()

    # 单次查询订单
    result = query.query_single_order(Order(order_id="250415-***************"))


    print(f"状态码: {result.error_code}")
    print(f"失败原因: {result.error_msg}")
    print(f"订单号: {result.order_id}")
    print(f"ERP系统: {result.erp_system}")
    print(f"账套ID: {result.account_id}")
    print(f"金额: {result.amount}")
    print(f"单据日期: {result.bill_date}")
    print(f"状态: {result.status}")
    print("-" * 30)


    # 定义订单
    # orders = [
    #     Order(order_id="P754465050048345791"),
    # ]

    # # 批量查询订单
    # results = query.query_batch_orders(orders)

    # # 输出结果
    # for result in results:
    #     print(f"状态码: {result.error_code}")
    #     print(f"失败原因: {result.error_msg}")
    #     print(f"订单号: {result.order_id}")
    #     print(f"ERP系统: {result.erp_system}")
    #     print(f"账套ID: {result.account_id}")
    #     print(f"金额: {result.amount}")
    #     print(f"单据日期: {result.bill_date}")
    #     print(f"状态: {result.status}")
    #     print("-" * 30)

if __name__ == '__main__':
    main()