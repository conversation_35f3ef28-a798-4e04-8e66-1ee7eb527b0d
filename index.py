from data_folder_utils import Folder<PERSON>anager
from db_manager import <PERSON><PERSON><PERSON><PERSON>, SettlementManager, TableFields, OrderStatus
from shop_manager.constants import PlatformType, PlatformName

# 初始化数据库管理器
db_path = FolderManager.get_db_file_path()
orders_manager = OrdersManager(db_path)
settlement_manager = SettlementManager(db_path)

# 插入订单数据示例
insert_data = {
    TableFields.ORDER_ID: "order003",
    TableFields.PLATFORM_TYPE: PlatformType.DOUYIN.value,
    TableFields.PLATFORM_NAME: PlatformName.DOUYIN_JIUYUN_FLAGSHIP.value,
    TableFields.MERCHANT_RECEIVABLE: 1000.00,
    TableFields.ORDER_AMOUNT: 1200.00,
    TableFields.RECEIVABLE_AMOUNT: 1000.00,
    TableFields.PAYMENT_CHANNEL: "alipay",
    TableFields.PAYMENT_DATE: "2024-03-25",
    TableFields.SALES_ORDER_DATE: "2025-01-25",
    TableFields.ORDER_STATUS: OrderStatus.PAID
}
success = orders_manager.insert_data(insert_data)

# settlement_manager = SettlementManager(FolderManager.get_db_file_path())
# data = {
#     TableFields.ORDER_ID: "order1233",
#     TableFields.PLATFORM_NAME: PlatformName.DOUYIN_JIUYUN_FLAGSHIP.value,
#     TableFields.PLATFORM_TYPE: PlatformType.DOUYIN.value,
#     TableFields.SETTLEMENT_TIME: "2024-03-21",
#     TableFields.SETTLEMENT_AMOUNT: 1000.00,
#     TableFields.SETTLEMENT_TYPE: "type_a",
#     TableFields.SETTLEMENT_CHANNEL: "channel_a"
# }
# success = settlement_manager.insert_data(data)

# update_data = {
#     TableFields.RECEIVED_AMOUNT: 800.00,
#     TableFields.PENDING_PAYMENT: 200.00,
#     TableFields.PAYMENT_DATE: "2024-03-26"
# }
# success = orders_manager.update_data("order002", update_data)

from db_manager.web_server import WebServer
web_server = WebServer(orders_manager, settlement_manager)
web_server.run()