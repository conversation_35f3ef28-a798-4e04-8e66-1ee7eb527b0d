from enum import Enum

class TableFields(Enum):
    """SQLite数据库表字段枚举类"""

    # 基础信息字段
    PLATFORM_TYPE = "platform_type"         # 平台类型
    PLATFORM_NAME = "platform_name"         # 平台名称
    MAIN_ORDER_ID = "main_order_id"        # 主订单号
    ORDER_ID = "order_id"                   # 订单ID
    PLATFORM_ID = 'platform_id'             # 平台id（店铺id）
    ORDER_STATUS = "order_status"           # 订单状态（已支付/已取消/已退款）
    SHIPPING_TIME = "shipping_time"         # 发货时间

    # 金额相关字段
    MERCHANT_RECEIVABLE = "merchant_receivable"   # 商家应收金额
    REFUND_AMOUNT = "refund_amount"              # 退款金额
    ORDER_AMOUNT = "order_amount"                # 订单金额
    RECEIVABLE_AMOUNT = "receivable_amount"      # 应收金额

    # U8C系统相关金额
    U8C_029_AMOUNT = "u8c_029_amount"           # U8C-029金额
    U8C_515_AMOUNT = "u8c_515_amount"           # U8C-515金额

    # T+系统相关金额
    T_PLUS_002_AMOUNT = "t_plus_002_amount"     # T+002金额
    T_PLUS_008_AMOUNT = "t_plus_008_amount"     # T+008金额

    # 销售和发货相关
    SALES_ORDER_TOTAL = "sales_order_total"     # 销货单合计
    SALES_ORDER_DATE = "sales_order_date"       # 销货单日期
    UNSHIPPED_AMOUNT = "unshipped_amount"       # 本月未发货金额

    # 回款相关信息
    RECEIVED_AMOUNT = "received_amount"         # 已回款金额
    PAYMENT_DATE = "payment_date"               # 回款日期
    PAYMENT_CHANNEL = "payment_channel"         # 收款渠道
    PENDING_PAYMENT = "pending_payment"         # 待回款

    # 结算单相关信息
    SETTLEMENT_TIME = "settlement_time"           # 结算时间
    SETTLEMENT_AMOUNT = "settlement_amount"       # 结算金额
    SETTLEMENT_TYPE = "settlement_type"           # 结算类型
    SETTLEMENT_CHANNEL = "settlement_channel"     # 收款渠道

    def __str__(self):
        return self.value