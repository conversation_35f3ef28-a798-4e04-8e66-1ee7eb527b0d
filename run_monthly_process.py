from datetime import datetime, timedelta

from data_folder_utils import FolderManager
from db_manager import SettlementManager
from settlement_downloader.downloader import SettlementDownloader
from settlement_downloader.platforms.xiaohongshu import Xiaohongshu
from settlement_downloader.platforms.taobao import <PERSON><PERSON>
from shop_manager.constants import PlatformType
from thirdparty_order_fetcher import <PERSON>hongshu<PERSON>rder<PERSON>etcher, TaobaoOrderFetcher
from vinehoo_order_fetcher.order_fetcher import OrderFetcher

def run_monthly_process(year: int, month: int):
    """执行月度数据处理流程"""
    print(f"\n开始处理 {year}年{month}月 的数据...")

    fetcher = OrderFetcher()
    # 1. 下载结算单数据
    # print("\n=== 步骤1: 下载结算单数据 ===")
    # db_path = FolderManager.get_db_file_path()
    # db_settlement = SettlementManager(db_path)
    #
    # downloader = SettlementDownloader(
    #     db_settlement,
    #     platforms=[Xiaohongshu(),Taobao()]
    # )
    #
    # # 设置时间范围
    # start_date = datetime(year, month, 1)
    # if month == 12:
    #     end_date = datetime(year + 1, 1, 1)
    # else:
    #     end_date = datetime(year, month + 1, 1)
    # end_date = end_date.replace(microsecond=0) - timedelta(seconds=1)
    #
    # # 执行下载
    # downloader.download_settlements(
    #     start_date=start_date,
    #     end_date=end_date,
    #     max_attempts=3
    # )

    # 2. 获取订单数据
    print("\n=== 步骤2: 获取订单数据 ===")
    # xhs_fetcher = XiaohongshuOrderFetcher()
    # xhs_orders = xhs_fetcher.fetch_orders(year, month)
    # print(f"获取到 {len(xhs_orders)} 个小红书订单")
    #TODO  小红书暂时屏蔽 开发淘宝

    # 获取淘宝订单数据
    tb_fetcher = TaobaoOrderFetcher()
    tb_orders = tb_fetcher.fetch_orders(year, month)
    print(f"获取到 {len(tb_orders)} 个淘宝订单")

    # 测试多个平台同步
    print("\n--- 测试多个平台同步 ---")
    try:
        platforms_to_sync = [
            PlatformType.XIAOHONGSHU,
            PlatformType.TAOBAO,
        ]
        synced_count = fetcher.sync_sales_data_to_mysql(
            year=year,
            month=month,
            platforms=platforms_to_sync
        )
        print(f"多个平台同步完成，同步记录数: {synced_count}")
    except Exception as e:
        print(f"多个平台同步失败: {str(e)}")

    # 4. 导出Excel文件
    # print("\n=== 步骤3: 导出Excel文件 ===")
    # exporter = ExcelExporter()
    # output_file = exporter.export_monthly_summary(
    #     year=year,
    #     month=month,
    #     platform=ExportPlatform.XIAOHONGSHU
    # )
    #
    # if output_file:
    #     print(f"\n处理完成! Excel文件已保存到: {output_file}")
    # else:
    #     print("\n处理完成，但没有数据需要导出")


if __name__ == "__main__":
    # 设置要处理的年月
    today = datetime.today()
    last_month = today.replace(day=1) - timedelta(days=1)
    YEAR = last_month.year
    MONTH = last_month.month
    MONTH = 3 #todo

    try:
        run_monthly_process(YEAR, MONTH)
    except Exception as e:
        print(f"\n处理过程中发生错误: {str(e)}")
