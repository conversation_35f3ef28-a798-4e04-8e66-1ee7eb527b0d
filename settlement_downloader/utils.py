from datetime import datetime, timezone, timedelta
import os

from db_manager.base_mysql import MySQLDB


def validate_date_range(start_date: datetime, end_date: datetime) -> None:
    """验证日期范围是否有效"""
    if start_date > end_date:
        raise ValueError("start_date must be earlier than end_date")


def ensure_directory_exists(path: str) -> None:
    """确保保存路径的目录存在"""
    directory = os.path.dirname(path)
    if directory and not os.path.exists(directory):
        os.makedirs(directory)


def get_first_day_of_month_timestamp(date=None) -> int:
    """
    获取指定日期所在月份的第一天0点的时间戳（秒级）。

    参数:
        date (datetime/str, 可选):
            - 如果为 None，使用当前时间；
            - 如果为 datetime 对象，计算该日期所在月的第一天；
            - 如果为字符串（如 "2025-12-30"），自动解析为日期。

    返回:
        int: 当月1日0点的时间戳（秒级）。
    """
    # 处理输入日期
    if date is None:
        now = datetime.now()
    elif isinstance(date, datetime):
        now = date
    elif isinstance(date, str):
        now = datetime.strptime(date, "%Y-%m-%d")
    else:
        raise ValueError("输入必须是 datetime 对象、日期字符串（如 '2025-12-30'）或 None")

    # 计算当月1日0点
    first_day = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    return int(first_day.timestamp())


def get_last_day_of_month_timestamp(date=None) -> int:
    """
    获取指定日期所在月份的最后一天23:59:59的时间戳（秒级）。

    参数:
        date (datetime/str, 可选):
            - 如果为 None，使用当前时间；
            - 如果为 datetime 对象，计算该日期所在月的最后一天；
            - 如果为字符串（如 "2025-12-30"），自动解析为日期。

    返回:
        int: 当月最后一天23:59:59的时间戳（秒级）。
    """
    # 处理输入日期
    if date is None:
        now = datetime.now()
    elif isinstance(date, datetime):
        now = date
    elif isinstance(date, str):
        now = datetime.strptime(date, "%Y-%m-%d")
    else:
        raise ValueError("输入必须是 datetime 对象、日期字符串（如 '2025-12-30'）或 None")

    # 计算下个月1日0点，然后减去1秒，得到本月最后一天23:59:59
    next_month = now.replace(day=28) + timedelta(days=4)  # 确保进入下个月
    first_day_next_month = next_month.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    last_day_current_month = first_day_next_month - timedelta(seconds=1)

    return int(last_day_current_month.timestamp())


def datetime_to_timestamp(
        time_str,
        format="%Y-%m-%d %H:%M:%S",
        timezone_offset=None,
        milliseconds=False
):
    """
    将时间字符串转换为时间戳（支持时区偏移和毫秒级输出）

    Args:
        time_str (str): 时间字符串（如 "2025-04-01 04:46:50"）
        format (str): 时间格式（默认 "%Y-%m-%d %H:%M:%S"）
        timezone_offset (int/str): 时区偏移（如 +8 表示东八区，"-05:00" 表示西五区）
        milliseconds (bool): 是否返回毫秒级时间戳（默认秒级）

    Returns:
        int: 时间戳（秒级或毫秒级）

    Raises:
        ValueError: 时间字符串或格式错误
    """
    try:
        # 解析时间字符串为 naive datetime 对象
        dt = datetime.strptime(time_str, format)

        # 处理时区
        if timezone_offset is not None:
            if isinstance(timezone_offset, int):
                # 整数形式（如 +8）
                tz = timezone(timedelta(hours=timezone_offset))
            elif isinstance(timezone_offset, str):
                # 字符串形式（如 "-05:00"）
                sign = -1 if timezone_offset.startswith("-") else 1
                hours, minutes = map(int, timezone_offset.replace("+", "").split(":"))
                tz = timezone(timedelta(hours=sign * hours, minutes=sign * minutes))
            else:
                raise ValueError("时区偏移必须是整数或字符串（如 '+8' 或 '-05:00'）")
            dt = dt.replace(tzinfo=tz)  # 添加时区信息

        # 转换为时间戳
        timestamp = dt.timestamp()

        # 返回秒级或毫秒级
        return int(timestamp * 1000) if milliseconds else int(timestamp)

    except ValueError as e:
        raise ValueError(f"时间格式错误: {e} (输入: '{time_str}', 格式: '{format}')")


def get_last_month_str(date=None) -> str:
    """
    获取上个月的 YYYYMM 格式字符串（如 "202503"）。

    参数:
        date (datetime/str, 可选):
            - 如果为 None，使用当前时间；
            - 如果为 datetime 对象，计算该日期的上个月；
            - 如果为字符串（如 "202504"），自动解析为日期。

    返回:
        str: 上个月的 YYYYMM 格式字符串。
    """
    # 处理输入日期
    if date is None:
        now = datetime.now()
    elif isinstance(date, datetime):
        now = date
    elif isinstance(date, str):
        now = datetime.strptime(date, "%Y%m")
    else:
        raise ValueError("输入必须是 datetime 对象、YYYYMM 格式字符串或 None")

    # 计算上个月最后一天（跨年自动处理）
    first_day_of_current_month = now.replace(day=1)
    last_day_of_last_month = first_day_of_current_month - timedelta(days=1)
    return last_day_of_last_month.strftime("%Y%m")
