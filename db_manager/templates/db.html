<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据表管理</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.3.1/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            padding: 20px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .table-container {
            margin-top: 20px;
            overflow-x: auto;
        }
        .pagination {
            margin-top: 20px;
        }
        .filter-form {
            margin-bottom: 20px;
        }
        .nav-tabs .nav-link {
            color: #495057;
            border: none;
            padding: 1rem 1.5rem;
            font-weight: 500;
        }
        .nav-tabs .nav-link.active {
            color: #0d6efd;
            border-bottom: 2px solid #0d6efd;
            background: none;
        }
        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .stats-card {
            transition: transform 0.2s;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 标题和表格切换 -->
        <div class="row mb-4">
            <div class="col">
                <h1 class="display-6 mb-4">数据表管理</h1>
                <ul class="nav nav-tabs" id="dataTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="orders-tab" data-bs-toggle="tab" data-bs-target="#orders" type="button" role="tab">
                            订单数据
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="settlements-tab" data-bs-toggle="tab" data-bs-target="#settlements" type="button" role="tab">
                            结算数据
                        </button>
                    </li>
                </ul>
            </div>
        </div>

        <div class="tab-content" id="dataTabsContent">
            <!-- 订单数据面板 -->
            <div class="tab-pane fade show active" id="orders" role="tabpanel">
                <!-- 筛选表单 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" action="/db" class="filter-form" id="ordersFilterForm">
                            <input type="hidden" name="table_type" value="orders">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label for="main_order_id" class="form-label">主订单号</label>
                                    <input type="text" class="form-control" id="main_order_id" name="main_order_id" value="{{ filters.main_order_id if 'main_order_id' in filters else '' }}">
                                </div>
                                <div class="col-md-4">
                                    <label for="order_id" class="form-label">子订单号</label>
                                    <input type="text" class="form-control" id="order_id" name="order_id" value="{{ filters.order_id if 'order_id' in filters else '' }}">
                                </div>
                                <div class="col-md-4">
                                    <label for="shipping_time" class="form-label">发货时间</label>
                                    <input type="date" class="form-control" id="shipping_time" name="shipping_time" value="{{ filters.shipping_time if 'shipping_time' in filters else '' }}">
                                </div>
                                <div class="col-md-4">
                                    <label for="platform_type" class="form-label">平台类型</label>
                                    <input type="text" class="form-control" id="platform_type" name="platform_type" value="{{ filters.platform_type if 'platform_type' in filters else '' }}">
                                </div>
                                <div class="col-md-4">
                                    <label for="platform_name" class="form-label">平台名称</label>
                                    <input type="text" class="form-control" id="platform_name" name="platform_name" value="{{ filters.platform_name if 'platform_name' in filters else '' }}">
                                </div>
                                <div class="col-md-4">
                                    <label for="sales_order_date" class="form-label">销货单日期</label>
                                    <input type="date" class="form-control" id="sales_order_date" name="sales_order_date" value="{{ filters.sales_order_date if 'sales_order_date' in filters else '' }}">
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col">
                                    <button type="submit" class="btn btn-primary">筛选</button>
                                    <button type="reset" class="btn btn-outline-secondary ms-2">重置</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 订单数据表格 -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-container">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>平台类型</th>
                                        <th>平台名称</th>
                                        <th>主订单号</th>
                                        <th>子订单号</th>
                                        <th>订单状态</th>
                                        <th>发货时间</th>
                                        <th>商家应收金额</th>
                                        <th>退款金额</th>
                                        <th>订单金额</th>
                                        <th>应收金额</th>
                                        <th>U8C-029金额</th>
                                        <th>U8C-515金额</th>
                                        <th>T+002金额</th>
                                        <th>T+008金额</th>
                                        <th>销货单合计</th>
                                        <th>销货单日期</th>
                                        <th>本月未发货金额</th>
                                        <th>已回款金额</th>
                                        <th>回款日期</th>
                                        <th>收款渠道</th>
                                        <th>待回款</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if data and table_type == 'orders' %}
                                        {% for row in data %}
                                        <tr>
                                            <td>{{ row.platform_type }}</td>
                                            <td>{{ row.platform_name }}</td>
                                            <td>{{ row.main_order_id }}</td>
                                            <td>{{ row.order_id }}</td>
                                            <td>{{ row.order_status }}</td>
                                            <td>{{ row.shipping_time|datetime('%Y-%m-%d') if row.shipping_time else '' }}</td>
                                            <td>{{ row.merchant_receivable }}</td>
                                            <td>{{ row.refund_amount }}</td>
                                            <td>{{ row.order_amount }}</td>
                                            <td>{{ row.receivable_amount }}</td>
                                            <td>{{ row.u8c_029_amount }}</td>
                                            <td>{{ row.u8c_515_amount }}</td>
                                            <td>{{ row.t_plus_002_amount }}</td>
                                            <td>{{ row.t_plus_008_amount }}</td>
                                            <td>{{ row.sales_order_total }}</td>
                                            <td>{{ row.sales_order_date }}</td>
                                            <td>{{ row.unshipped_amount }}</td>
                                            <td>{{ row.received_amount }}</td>
                                            <td>{{ row.payment_date }}</td>
                                            <td>{{ row.payment_channel }}</td>
                                            <td>{{ row.pending_payment }}</td>
                                        </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="21" class="text-center">暂无数据</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 结算数据面板 -->
            <div class="tab-pane fade" id="settlements" role="tabpanel">
                <!-- 筛选表单 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" action="/db" class="filter-form" id="settlementsFilterForm">
                            <input type="hidden" name="table_type" value="settlements">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <label for="settlement_order_id" class="form-label">订单号</label>
                                    <input type="text" class="form-control" id="settlement_order_id" name="order_id" value="{{ filters.order_id if 'order_id' in filters else '' }}">
                                </div>
                                <div class="col-md-3">
                                    <label for="settlement_platform_type" class="form-label">平台类型</label>
                                    <input type="text" class="form-control" id="settlement_platform_type" name="platform_type" value="{{ filters.platform_type if 'platform_type' in filters else '' }}">
                                </div>
                                <div class="col-md-3">
                                    <label for="settlement_platform_name" class="form-label">平台名称</label>
                                    <input type="text" class="form-control" id="settlement_platform_name" name="platform_name" value="{{ filters.platform_name if 'platform_name' in filters else '' }}">
                                </div>
                                <div class="col-md-3">
                                    <label for="settlement_type" class="form-label">结算类型</label>
                                    <input type="text" class="form-control" id="settlement_type" name="settlement_type" value="{{ filters.settlement_type if 'settlement_type' in filters else '' }}">
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col">
                                    <button type="submit" class="btn btn-primary">筛选</button>
                                    <button type="reset" class="btn btn-outline-secondary ms-2">重置</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 结算数据表格 -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-container">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>平台类型</th>
                                        <th>平台名称</th>
                                        <th>订单ID</th>
                                        <th>结算时间</th>
                                        <th>结算金额</th>
                                        <th>结算类型</th>
                                        <th>收款渠道</th>
                                        <th>创建时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if data and table_type == 'settlements' %}
                                        {% for row in data %}
                                        <tr>
                                            <td>{{ row.platform_type }}</td>
                                            <td>{{ row.platform_name }}</td>
                                            <td>{{ row.order_id }}</td>
                                            <td>{{ row.settlement_time }}</td>
                                            <td>{{ row.settlement_amount }}</td>
                                            <td>{{ row.settlement_type }}</td>
                                            <td>{{ row.settlement_channel }}</td>
                                            <td>{{ row.created_at }}</td>
                                        </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="8" class="text-center">暂无数据</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        {% if total_pages > 1 %}
        <nav class="mt-4">
            <ul class="pagination justify-content-center">
                <li class="page-item {% if page == 1 %}disabled{% endif %}">
                    <a class="page-link" href="?page=1&table_type={{ table_type }}{% for key, value in filters.items() %}&{{ key }}={{ value }}{% endfor %}">
                        首页
                    </a>
                </li>
                <li class="page-item {% if page == 1 %}disabled{% endif %}">
                    <a class="page-link" href="?page={{ page - 1 }}&table_type={{ table_type }}{% for key, value in filters.items() %}&{{ key }}={{ value }}{% endfor %}">
                        上一页
                    </a>
                </li>
                {% for p in range(max(1, page - 2), min(total_pages + 1, page + 3)) %}
                <li class="page-item {% if p == page %}active{% endif %}">
                    <a class="page-link" href="?page={{ p }}&table_type={{ table_type }}{% for key, value in filters.items() %}&{{ key }}={{ value }}{% endfor %}">
                        {{ p }}
                    </a>
                </li>
                {% endfor %}
                <li class="page-item {% if page == total_pages %}disabled{% endif %}">
                    <a class="page-link" href="?page={{ page + 1 }}&table_type={{ table_type }}{% for key, value in filters.items() %}&{{ key }}={{ value }}{% endfor %}">
                        下一页
                    </a>
                </li>
                <li class="page-item {% if page == total_pages %}disabled{% endif %}">
                    <a class="page-link" href="?page={{ total_pages }}&table_type={{ table_type }}{% for key, value in filters.items() %}&{{ key }}={{ value }}{% endfor %}">
                        末页
                    </a>
                </li>
            </ul>
        </nav>
        {% endif %}

        <!-- 数据统计 -->
        <div class="row mt-4">
            <div class="col text-end">
                <p class="text-muted">
                    共 {{ total }} 条记录，当前第 {{ page }}/{{ total_pages }} 页
                </p>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.3.1/js/bootstrap.bundle.min.js"></script>

    <!-- 页面交互脚本 -->
    <script>
        // 保持选中的标签页在页面刷新后
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const tableType = urlParams.get('table_type') || 'orders';

            const tab = document.querySelector(`#${tableType}-tab`);
            if (tab) {
                const bsTab = new bootstrap.Tab(tab);
                bsTab.show();
            }

            // 添加标签页切换事件监听
            ['orders-tab', 'settlements-tab'].forEach(tabId => {
                document.getElementById(tabId).addEventListener('click', function(e) {
                    e.preventDefault();
                    const newTableType = tabId.replace('-tab', '');
                    window.location.href = `?table_type=${newTableType}`;
                });
            });
        });

        // 重置按钮功能
        document.querySelectorAll('button[type="reset"]').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const form = this.closest('form');
                const tableType = form.querySelector('input[name="table_type"]').value;
                form.reset();
                // 重新设置 table_type，因为 reset() 会清除所有表单值
                form.querySelector('input[name="table_type"]').value = tableType;
                // 只保留 table_type 参数的 URL
                window.location.href = `?table_type=${tableType}`;
            });
        });
    </script>
</body>
</html>