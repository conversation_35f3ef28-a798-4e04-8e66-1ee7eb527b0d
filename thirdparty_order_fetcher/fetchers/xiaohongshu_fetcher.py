import hashlib
import time
from datetime import datetime, timedelta, date
from typing import List, Dict, Optional, Any

import requests

from data_folder_utils import FolderManager
from db_manager import OrdersManager, TableFields
from db_manager.order_status import OrderStatus  # 确保导入OrderStatus
from erp_order_query import OrderQuery, Order as ERPOrder
from settlement_downloader.platform_shop_config import PlatformShopConfig
from shop_manager.constants import PlatformName, PlatformType
from ..base_fetcher import BaseOrderFetcher
from ..exceptions import PlatformRunError, APIConnectionError
from ..models import Order


class XiaohongshuOrderFetcher(BaseOrderFetcher):
    # API基础URL
    API_URL = "https://ark.xiaohongshu.com/ark/open_api/v3/common_controller"
    API_VERSION = "2.0"
    
    # 小红书应用凭证配置 - 支持多个应用
    XHS_APP_CONFIGS = {
        # 第一个应用凭证
        'app1': {
            'app_id': 'e840e096a975478984d4',
            'app_secret': 'c7931a95f48312d7918cde5de2c47b8e'
        },
        # 第二个应用凭证
        'app2': {
            'app_id': '4787a2382e784ccb9b09',
            'app_secret': '5a98d1879df35434299a7b7c62e9cb2b'
        }
    }
    
    # 店铺与应用的映射关系
    SHOP_APP_MAPPING = {
        # 示例: '店铺ID': '应用配置键名'
        '62b98b750d601800010dc853': 'app1',  # 行吟信息科技（武汉）有限公司(云酒网小红书店）使用app1
        '650a60e17fa15200013acf16': 'app1',  # 木兰朵-小红书使用app1
        '65113b63effd830001ca90e0': 'app2',  # 小红书-威哥蒸馏所使用app2
        '653b599dbffe730001559bd6': 'app2',  # 小红书-Brown Brothers布琅兄弟使用app2
        # 添加其他店铺映射...
    }
    
    # 订单状态映射
    ORDER_STATUS_MAP = {
        1: "PENDING",       # 已下单待付款
        2: "PROCESSING",    # 已支付处理中
        3: "PROCESSING",    # 清关中
        4: "PROCESSING",    # 待发货
        5: "SHIPPED",       # 部分发货
        6: "SHIPPED",       # 待收货
        7: "COMPLETED",     # 已完成
        8: "CLOSED",        # 已关闭
        9: "CANCELLED",     # 已取消
        10: "PROCESSING"    # 换货申请中
    }
    
    def __init__(self):
        super().__init__(
            platform_name=PlatformName.XHS_YUNWINE,
            platform_type=PlatformType.XIAOHONGSHU
        )
        # 从PlatformShopConfig获取小红书店铺配置
        self.platform_shop_config = PlatformShopConfig()
        self.shops = self.platform_shop_config.get_shops_by_platform(PlatformType.XIAOHONGSHU.value)
        
        # 初始化订单管理器，用于保存订单到本地数据库
        self.orders_manager = OrdersManager(FolderManager.get_db_file_path())
        
        # 确保订单处理记录表存在
        self._ensure_order_process_table_exists()
        
        print(f"从配置中获取到 {len(self.shops)} 个小红书店铺")
        for shop in self.shops:
            print(f"  - {shop}")
            
        # 初始化所有店铺信息
        self.shops_info = self._get_all_shops_info()
        self.order_query = OrderQuery()  # 初始化ERP订单查询器
    
    def _ensure_order_process_table_exists(self):
        """确保订单处理记录表存在"""
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS order_process_records (
            order_id TEXT PRIMARY KEY,
            process_status INTEGER DEFAULT 0,  -- 0=待处理, 1=成功
            process_date TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        try:
            self.orders_manager.execute_query(create_table_sql)
            # 获取当前日期
            today_date = date.today().strftime('%Y-%m-%d')
            
            # 删除非当日的订单处理记录
            delete_old_records_sql = """
            DELETE FROM order_process_records
            WHERE process_date != ?
            """
            
            try:
                result = self.orders_manager.execute_query(delete_old_records_sql, (today_date,))
                print(f"已清理非当日({today_date})的历史订单处理记录")
            except Exception as e:
                print(f"清理历史订单处理记录失败: {str(e)}")
                
            print("订单处理记录表初始化成功")
        except Exception as e:
            print(f"初始化订单处理记录表失败: {str(e)}")
            raise
    
    def _save_order_ids_to_process_table(self, order_ids: set, today_date: str):
        """将订单ID保存到处理表中"""
        if not order_ids:
            return
        
        try:
            # 查询已存在的订单记录
            existing_order_ids = set()
            for order_id in order_ids:
                query = "SELECT order_id FROM order_process_records WHERE order_id = ?"
                result = self.orders_manager.execute_query_one(query, (order_id,))
                if result:
                    existing_order_ids.add(result[0])
            
            # 需要插入的新订单ID
            new_order_ids = order_ids - existing_order_ids
            
            # 插入新订单记录
            if new_order_ids:
                insert_query = """
                INSERT INTO order_process_records (order_id, process_status, process_date)
                VALUES (?, 0, ?)
                """
                
                for order_id in new_order_ids:
                    try:
                        self.orders_manager.execute_query(insert_query, (order_id, today_date))
                    except Exception as e:
                        print(f"插入订单处理记录失败 ({order_id}): {str(e)}")
                
                print(f"成功添加 {len(new_order_ids)} 个新订单到处理表")
            else:
                print("没有新订单需要添加到处理表")
                
        except Exception as e:
            print(f"保存订单ID到处理表失败: {str(e)}")
    
    def _get_pending_order_ids(self, order_ids: set, today_date: str) -> set:
        """获取今日待处理的订单ID"""
        if not order_ids:
            return set()
        
        pending_order_ids = set()
        
        try:
            for order_id in order_ids:
                query = """
                SELECT order_id FROM order_process_records 
                WHERE order_id = ? AND process_date = ? AND process_status = 0
                """
                result = self.orders_manager.execute_query_one(query, (order_id, today_date))
                if result:
                    pending_order_ids.add(result[0])
            
            print(f"找到 {len(pending_order_ids)} 个待处理订单")
            return pending_order_ids
        except Exception as e:
            print(f"获取待处理订单ID失败: {str(e)}")
            return set()
    
    def _mark_order_as_processed(self, order_id: str):
        """将订单标记为已处理"""
        update_query = """
        UPDATE order_process_records
        SET process_status = 1
        WHERE order_id = ?
        """
        
        try:
            self.orders_manager.execute_query(update_query, (order_id,))
            print(f"订单 {order_id} 已标记为处理成功")
        except Exception as e:
            print(f"标记订单为已处理失败 ({order_id}): {str(e)}")
    
    def _get_all_shops_info(self) -> Dict[str, dict]:
        """获取所有小红书店铺信息"""
        shops_info = {}
        
        print(f"正在获取 {len(self.shops)} 个小红书店铺信息...")
        
        for shop in self.shops:
            shop_id = shop.shop_id
            shop_name = shop.shop_name
            
            try:
                shop_info = shop.get_shop_info()
                if shop_info is None:
                    print(f"警告: 店铺 {shop_name}({shop_id}) 信息获取失败，将跳过")
                    continue
                
                # 确定该店铺使用哪个应用配置
                app_key = self.SHOP_APP_MAPPING.get(shop_id, 'app1')  # 默认使用app1
                app_config = self.XHS_APP_CONFIGS.get(app_key)
                
                if not app_config:
                    print(f"警告: 店铺 {shop_name}({shop_id}) 没有对应的应用配置，将跳过")
                    continue
                
                # 动态添加API调用所需的属性
                shop.app_id = app_config['app_id']
                shop.app_secret = app_config['app_secret']
                
                # 从shop_info中获取access_token
                if 'access_token' in shop_info:
                    shop.access_token = shop_info['access_token']
                else:
                    print(f"警告: 店铺 {shop_name}({shop_id}) 缺少access_token，将跳过")
                    continue
                
                shops_info[shop_id] = shop_info
                print(f"成功获取店铺信息: {shop_name}({shop_id})，使用应用配置: {app_key}")
            except Exception as e:
                print(f"错误: 获取店铺 {shop_name}({shop_id}) 信息时出错: {str(e)}")
        
        if not shops_info:
            raise PlatformRunError("所有小红书店铺信息获取失败")
            
        print(f"成功获取 {len(shops_info)} 个小红书店铺信息")
        return shops_info
    
    def _check_pending_orders(self, year: int, month: int, shop_id: Optional[str] = None) -> bool:
        """
        检查当日是否还有待处理的订单
        
        Args:
            year: 年份
            month: 月份
            shop_id: 指定店铺ID
            
        Returns:
            是否有待处理订单
        """
        today_date = date.today().strftime('%Y-%m-%d')
        
        # 根据shop_id构建查询条件
        shop_condition = ""
        params = [today_date]
        
        shops_to_check = [s.shop_id for s in self.shops if s.shop_id == shop_id] if shop_id else [s.shop_id for s in self.shops]
        
        # 检查订单处理表中是否有待处理的订单
        query = """
        SELECT COUNT(*) FROM order_process_records 
        WHERE process_date = ? AND process_status = 0
        """
        
        try:
            count = self.orders_manager.execute_query_one(query, tuple(params))
            pending_count = count[0] if count else 0
            
            print(f"当日还有 {pending_count} 个订单待处理")
            
            return pending_count > 0
            
        except Exception as e:
            print(f"检查待处理订单失败: {str(e)}")
            return False
    
    def fetch_orders(self, year: int, month: int, shop_id: Optional[str] = None) -> List[Order]:
        """
        获取指定月份的订单
        
        Args:
            year: 年份
            month: 月份
            shop_id: 指定店铺ID，如果为None则获取所有店铺订单
            
        Returns:
            订单列表
        """
        self.validate_date(year, month)
        
        if not self.shops_info:
            raise PlatformRunError("小红书店铺信息未初始化")

        all_orders = []
        max_fetch_iterations = 10  # 最大重试次数，避免无限循环
        
        for iteration in range(max_fetch_iterations):
            if iteration > 0:
                print(f"第 {iteration+1} 次尝试获取待处理订单...")
            
            # 确定要处理的店铺
            shops_to_process = [s for s in self.shops if s.shop_id == shop_id] if shop_id else self.shops
            
            current_iteration_orders = []
            
            for shop in shops_to_process:
                try:
                    shop_name = next((s.shop_name for s in self.shops if s.shop_id == shop.shop_id), shop.shop_id)
                    print(f"正在获取店铺 {shop_name} 的订单...")
                    
                    # 获取该店铺指定月份的所有订单
                    shop_orders = self._fetch_orders_for_month(shop, year, month)
                    
                    current_iteration_orders.extend(shop_orders)
                    print(f"成功获取 {len(shop_orders)} 个订单")
                    
                except Exception as e:
                    print(f"错误: 获取店铺 {shop.shop_id} 订单时出错: {str(e)}")
            
            all_orders.extend(current_iteration_orders)
            
            # 检查是否还有待处理的订单
            has_pending_orders = self._check_pending_orders(year, month, shop_id)
            
            if not has_pending_orders or not current_iteration_orders:
                # 如果没有待处理订单或者本次没有获取到订单，就退出循环
                break
                
            # 等待一段时间后继续下一轮获取
            if iteration < max_fetch_iterations - 1 and has_pending_orders:
                wait_time = 5  # 等待5秒
                print(f"还有订单待处理，{wait_time}秒后将进行下一轮获取...")
                time.sleep(wait_time)
        
        total_processed = len(all_orders)
        print(f"所有轮次共获取处理了 {total_processed} 个订单")
        
        # 再次检查是否还有未处理的订单
        if self._check_pending_orders(year, month, shop_id):
            print("警告: 仍有部分订单未能成功处理，可以稍后再次运行来尝试处理这些订单")
        else:
            print("所有订单已成功处理")
            
        return all_orders
    
    def _save_to_local_db(self, order: Order):
        """保存订单到本地数据库"""
        try:
            # 获取订单数据
            order_data = getattr(order, 'order_data', {})

            # 获取订单状态和售后状态
            order_status = order_data.get('order_status', 0)
            after_sales_status = order_data.get('after_sales_status', 1)
            
            # 过滤掉不需要的订单状态
            if order_status in [1]:  # 待付款
                print(f"订单 {order.order_id} 状态为 {order_status}，跳过保存")
                return
            
            # 映射订单状态
            try:
                status = None
                if order_status == 9:  # 已取消
                    status = OrderStatus.CANCELLED
                elif order_status in [7,8,10]:  # 已完成、已关闭、换货申请中
                    if after_sales_status in [3]:  # 有售后
                        status = OrderStatus.REFUNDED
                    else:
                        status = OrderStatus.COMPLETED  # 使用 COMPLETED 枚举值
                elif order_status in [5, 6]:  # 部分发货、待收货
                    status = OrderStatus.SHIPPED
                elif order_status in [2, 3, 4]:  # 已支付处理中、清关中、待发货
                    status = OrderStatus.PAID
                
                if status is None:
                    print(f"订单 {order.order_id} 状态 {order_status} 无法映射，跳过保存")
                    return

                # 验证状态值是否为有效的枚举值
                if not isinstance(status, OrderStatus):
                    raise ValueError(f"Invalid order status: {status}")

                shop_id = order_data.get('shop_id', '')
                shop_name = order_data.get('shop_name', '')
                payment_amount = order_data.get('payment_amount', 0)
                merchant_receive = order_data.get('merchant_receive', 0)  # 商家实收金额
                refund_status = order_data.get('after_sales_status', 1)  # 售后状态
                
                # 从shops列表中获取店铺名称
                platform_name = next(
                    (shop.shop_name for shop in self.shops if shop.shop_id == shop_id),
                    shop_name  # 如果在self.shops中找不到，使用order_data中的shop_name作为后备
                )
                
                # 判断是否为退款订单
                is_refund = refund_status == 3  # 售后状态大于1表示有售后
                refund_amount = merchant_receive if is_refund else 0  # 如果是退款订单，退款金额等于支付金额
                
                # 判断是否已发货
                is_shipped = order.shipping_time is not None
                unshipped_amount = 0 if is_shipped else payment_amount  # 未发货金额
                
                # 判断是否已回款（假设已支付就是已回款）
                is_paid = order.payment_time is not None
                received_amount = payment_amount if is_paid else 0  # 已回款金额
                pending_payment = 0 if is_paid else payment_amount  # 待回款金额

                # 计算商家应收金额
                if status in [OrderStatus.CANCELLED,OrderStatus.REFUNDED]:
                    # 如果是已取消状态，商家应收 = 订单金额 - 退款金额
                    refund_amount = merchant_receive
                    merchant_receivable = merchant_receive - refund_amount
                else:
                    # 其他状态，商家应收 = 商家应收金额
                    merchant_receivable = merchant_receive

                # 根据店铺ID确定支付渠道
                payment_channel = ""  # 默认为空字符串
                if shop_id == '62b98b750d601800010dc853':  # 行吟信息科技（武汉）有限公司
                    payment_channel = "行吟小红书"
                elif shop_id == '65113b63effd830001ca90e0':  # 小红书-威哥蒸馏所
                    payment_channel = "小红书 - 威哥蒸馏所"
                elif shop_id == '653b599dbffe730001559bd6':  # 小红书-Brown Brothers布琅兄弟
                    payment_channel = "小红书-Brown Brothers布琅兄弟"
                # 其他店铺（兔子小红书）保持为空字符串

                # 查询ERP订单信息
                erp_result = self.order_query.query_single_order(ERPOrder(order_id=order.order_id))
                
                # 获取结算单数据
                settlements_query = """
                SELECT 
                    SUM(settlement_amount) as total_settlement,
                    MAX(settlement_time) as latest_settlement
                FROM settlements 
                WHERE order_id = ?
                """
                result = self.orders_manager.execute_query_one(settlements_query, (order.order_id,))

                # 初始化ERP相关金额
                u8c_029_amount = 0
                u8c_515_amount = 0
                t_plus_002_amount = 0
                t_plus_008_amount = 0

                bill_date=""
                # 根据ERP查询结果更新金额
                if erp_result.error_code == 0:  # 查询成功
                    amount = float(erp_result.amount)
                    bill_date = erp_result.bill_date
                    # 根据账套ID分配金额
                    if erp_result.erp_system == "U8C":
                        if erp_result.account_id == "u8c_029":
                            u8c_029_amount = amount
                        elif erp_result.account_id == "u8c_515":
                            u8c_515_amount = amount
                    elif erp_result.erp_system == "T+":
                        if erp_result.account_id == "t_plus_002":
                            t_plus_002_amount = amount
                        elif erp_result.account_id == "t_plus_008":
                            t_plus_008_amount = amount
                
                # 计算销货单合计
                sales_order_total = (u8c_029_amount + u8c_515_amount + 
                                    t_plus_002_amount + t_plus_008_amount)

                # 从结算单获取回款信息
                if result:
                    total_settlement, latest_settlement = result
                    received_amount = total_settlement or 0
                    payment_date = latest_settlement
                else:
                    received_amount = 0
                    payment_date = None

                # 获取发货时间（毫秒时间戳）并转换为秒级时间戳
                delivery_time_ms = order_data.get('deliveryTime', None)
                delivery_time_sec = int(delivery_time_ms / 1000) if delivery_time_ms is not None else None

                # 更新数据字典
                data = {
                    TableFields.ORDER_ID: order.order_id,
                    TableFields.SALES_ORDER_DATE: bill_date ,
                    TableFields.MAIN_ORDER_ID: order.order_id,
                    TableFields.PLATFORM_TYPE: self.platform_type.value,
                    TableFields.PLATFORM_NAME: platform_name,
                    TableFields.REFUND_AMOUNT: refund_amount,
                    TableFields.ORDER_AMOUNT: merchant_receive,
                    TableFields.RECEIVABLE_AMOUNT: merchant_receivable,
                    TableFields.MERCHANT_RECEIVABLE: merchant_receive,
                    TableFields.PAYMENT_CHANNEL: payment_channel,
                    TableFields.ORDER_STATUS: status,
                    TableFields.RECEIVED_AMOUNT: received_amount,  # 使用结算单总金额
                    TableFields.PAYMENT_DATE: payment_date,  # 使用最新结算时间
                    TableFields.PENDING_PAYMENT: max(0, merchant_receivable - received_amount),  # 待回款 = 应收金额 - 已回款金额
                    TableFields.U8C_029_AMOUNT: u8c_029_amount,
                    TableFields.U8C_515_AMOUNT: u8c_515_amount,
                    TableFields.T_PLUS_002_AMOUNT: t_plus_002_amount,
                    TableFields.T_PLUS_008_AMOUNT: t_plus_008_amount,
                    TableFields.SALES_ORDER_TOTAL: sales_order_total,
                    TableFields.UNSHIPPED_AMOUNT: unshipped_amount,
                    TableFields.SHIPPING_TIME: delivery_time_sec,  # 存储秒级时间戳
                }

                # 打印调试信息
                print(f"订单 {order.order_id} 状态映射: {order_status} -> {status.value}")

                try:
                    # 检查订单是否存在
                    exists = self.orders_manager.order_exists(str(order.order_id))
                    
                    if exists:
                        # 订单存在，执行更新
                        self.orders_manager.update_data(order.order_id, data)
                        print(f"更新订单: {order.order_id}")
                    else:
                        # 订单不存在，执行插入
                        self.orders_manager.insert_data(data)
                        print(f"插入订单: {order.order_id}")
                    
                    # 标记订单为已处理成功
                    self._mark_order_as_processed(order.order_id)
                    
                except Exception as e:
                    print(f"保存订单到本地数据库失败: {str(e)}")
            except Exception as e:
                print(f"订单状态处理错误 ({order.order_id}): {str(e)}")
                return
        except Exception as e:
            print(f"订单状态处理错误 ({order.order_id}): {str(e)}")
            return
    
    def _fetch_orders_for_month(self, shop, year: int, month: int) -> List[Order]:
        """获取指定月份的订单"""
        # 计算月初和月末时间
        start_date = datetime(year, month, 1)  # 月初
        if month == 12:
            end_date = datetime(year + 1, 1, 1) - timedelta(seconds=1)  # 月末最后一秒
        else:
            end_date = datetime(year, month + 1, 1) - timedelta(seconds=1)  # 月末最后一秒

        # 由于API限制每次查询时间间隔不能超过24小时
        # 将整月时间分成24小时的时间窗口
        time_windows = []
        current_date = start_date
        while current_date < end_date:
            # 计算24小时后的时间点，但不超过月末
            window_end = min(current_date + timedelta(hours=24), end_date)
            time_windows.append((
                int(current_date.timestamp()),
                int(window_end.timestamp())
            ))
            current_date = window_end + timedelta(seconds=1)
        
        print(f"查询时间范围: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
        print(f"分成 {len(time_windows)} 个24小时时间窗口")
        
        # 获取今天的日期
        today_date = date.today().strftime('%Y-%m-%d')
        
        # 获取所有时间窗口的订单ID
        all_order_ids = set()
        for start_time, end_time in time_windows:
            try:
                window_start = datetime.fromtimestamp(start_time)
                window_end = datetime.fromtimestamp(end_time)
                print(f"正在获取时间窗口 {window_start.strftime('%Y-%m-%d %H:%M:%S')} - {window_end.strftime('%Y-%m-%d %H:%M:%S')} 的订单...")
                
                window_order_ids = self._fetch_order_ids_for_window(
                    shop, start_time, end_time
                )
                all_order_ids.update(window_order_ids)
                
                print(f"该时间窗口获取到 {len(window_order_ids)} 个订单")
                
                # 添加短暂延迟，避免请求过于频繁

            except Exception as e:
                print(f"警告: 获取时间窗口 {window_start.strftime('%Y-%m-%d %H:%M:%S')} - "
                      f"{window_end.strftime('%Y-%m-%d %H:%M:%S')} 的订单失败: {str(e)}")
                # 发生错误时增加延迟
                time.sleep(0.5)

        print(f"总共获取到 {len(all_order_ids)} 个订单ID")
        
        # 将所有订单ID保存到处理表中
        self._save_order_ids_to_process_table(all_order_ids, today_date)
        
        # 获取待处理的订单ID
        pending_order_ids = self._get_pending_order_ids(all_order_ids, today_date)
        
        # 获取订单详情
        orders = []
        total_orders = len(pending_order_ids)
        print(f"需要处理 {total_orders} 个订单（今日待处理状态）")
        
        for index, order_id in enumerate(pending_order_ids, 1):
            try:
                print(f"正在获取订单详情 ({index}/{total_orders}): {order_id}")
                max_retries = 5
                retry_delay = 0.5  # 初始延迟0.5秒
                
                for attempt in range(max_retries):
                    try:
                        order = self._fetch_order_detail(shop, order_id)
                        if order:
                            # 保存订单到本地数据库
                            self._save_to_local_db(order)
                            orders.append(order)
                        break  # 成功则退出重试循环
                    except Exception as e:
                        if attempt < max_retries - 1:  # 最后一次尝试不睡眠
                            print(f"第 {attempt+1} 次尝试失败，{retry_delay}秒后重试...")
                            time.sleep(retry_delay)
                            retry_delay *= 2  # 指数退避
                        else:
                            print(f"订单 {order_id} 获取失败，已达最大重试次数")
            except Exception as e:
                print(f"警告: 获取订单 {order_id} 详情失败: {str(e)}")
        
        print(f"成功获取 {len(orders)} 个订单详情")
        return orders
    
    def _fetch_order_ids_for_window(self, shop, start_time: int, end_time: int) -> List[str]:
        """获取指定时间窗口的订单ID列表"""
        order_ids = []
        page_no = 1
        page_size = 100
        max_retries = 5
        retry_delay = 1.0
        
        while True:
            retry_count = 0
            success = False
            
            while retry_count < max_retries and not success:
                try:
                    # 调用订单列表API
                    response = self._call_api(
                        shop,
                        method="order.getOrderList",
                        biz_params={
                            "startTime": start_time,
                            "endTime": end_time,
                            "timeType": 1,  # 创建时间
                            "pageNo": page_no,
                            "pageSize": page_size
                        }
                    )
                    
                    # 解析响应
                    order_list = response.get("orderList", [])
                    for order in order_list:
                        order_ids.append(order["orderId"])
                    
                    # 检查是否有更多页
                    total = response.get("total", 0)
                    
                    success = True
                    
                    # 如果已经获取完所有页，退出循环
                    if page_no * page_size >= total:
                        return order_ids
                    
                    # 否则继续下一页
                    page_no += 1
                    
                except Exception as e:
                    retry_count += 1
                    if retry_count < max_retries:
                        print(f"获取订单列表失败(第{retry_count}次): {str(e)}，{retry_delay}秒后重试...")
                        time.sleep(retry_delay)
                        # 指数退避策略
                        retry_delay = min(retry_delay * 2, 30)  # 最长等待30秒
                    else:
                        print(f"获取订单列表失败，已达最大重试次数: {str(e)}")
                        raise APIConnectionError(f"获取订单列表失败: {str(e)}")
        
        return order_ids
    
    def _fetch_order_detail(self, shop, order_id: str) -> Optional[Order]:
        """获取订单详情并转换为Order对象"""
        try:
            # 调用订单详情API
            response = self._call_api(
                shop,
                method="order.getOrderDetail",
                biz_params={"orderId": order_id}
            )

            print(response)

            # 转换为Order对象
            return self._convert_to_order_object(shop, response)
            
        except Exception as e:
            raise APIConnectionError(f"获取订单详情失败: {str(e)}")
    
    def _convert_to_order_object(self, shop, order_data: Dict[str, Any]) -> Order:
        """将API返回的订单数据转换为Order对象"""
        # 获取订单状态
        order_status = order_data.get("orderStatus", 0)
        payment_status = self.ORDER_STATUS_MAP.get(order_status, "UNKNOWN")
        
        # 转换时间戳
        created_time = self._convert_timestamp(order_data.get("createdTime"))
        paid_time = self._convert_timestamp(order_data.get("paidTime"))
        delivery_time = self._convert_timestamp(order_data.get("deliveryTime"))
        
        # 计算金额（分转元）
        payment_amount = order_data.get("totalPayAmount", 0) / 100.0
        
        # 创建Order对象 - 只使用基本参数
        order = Order(
            order_id=order_data.get("orderId", ""),
            payment_status=payment_status,
            payment_time=paid_time,
            shipping_time=delivery_time
        )
        
        # 将其他信息存储在字典中，以便在_save_to_local_db中使用
        order.order_data = {
            "shop_id": shop.shop_id,
            "shop_name": shop.shop_name,
            "order_type": order_data.get("orderType"),
            "order_status": order_status,
            "after_sales_status": order_data.get("orderAfterSalesStatus"),
            "cancel_status": order_data.get("cancelStatus"),
            "created_time": created_time,
            "finish_time": self._convert_timestamp(order_data.get("finishTime")),
            "payment_amount": payment_amount,
            "shipping_fee": order_data.get("totalShippingFree", 0) / 100.0,
            "merchant_discount": order_data.get("totalMerchantDiscount", 0) / 100.0,
            "platform_discount": order_data.get("totalRedDiscount", 0) / 100.0,
            "merchant_receive": order_data.get("merchantActualReceiveAmount", 0) / 100.0,
            "express_no": order_data.get("expressTrackingNo"),
            "express_company": order_data.get("expressCompanyCode"),
            "customer_remark": order_data.get("customerRemark"),
            "seller_remark": order_data.get("sellerRemark"),
            "sku_list": order_data.get("skuList", []),
            "deliveryTime": order_data.get("deliveryTime")  # 添加发货时间（毫秒时间戳）
        }
        
        return order
    
    def _convert_timestamp(self, timestamp) -> Optional[datetime]:
        """将毫秒时间戳转换为datetime对象"""
        if not timestamp:
            return None
        
        # 毫秒转秒
        seconds = timestamp / 1000.0
        return datetime.fromtimestamp(seconds)
    
    def _call_api(self, shop, method: str, biz_params: Dict[str, Any]) -> Dict[str, Any]:
        """调用小红书开放平台API"""
        # 构建通用参数
        timestamp = str(int(time.time()))
        
        # 构建请求体
        request_body = {
            "appId": shop.app_id,
            "timestamp": timestamp,
            "version": self.API_VERSION,
            "method": method,
            # 添加业务参数
            **biz_params
        }
        
        # 计算签名
        sign = self._calculate_sign(method, shop.app_id, timestamp, self.API_VERSION, shop.app_secret)
        request_body["sign"] = sign
        
        # 如果需要授权，添加accessToken（不参与签名）
        if hasattr(shop, 'access_token') and shop.access_token:
            request_body["accessToken"] = shop.access_token
        
        # 发送请求
        max_retries = 3
        retry_delay = 1.0
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                headers = {"Content-Type": "application/json;charset=utf-8"}
                response = requests.post(
                    self.API_URL, 
                    json=request_body, 
                    headers=headers, 
                    timeout=60  # 增加超时时间到60秒
                )
                response.raise_for_status()
                
                data = response.json()
                
                # 检查API响应状态
                if data.get("error_code") != 0:
                    error_msg = data.get("error_msg", "未知错误")
                    error_code = data.get("error_code", -1)
                    
                    # 如果是token过期等可恢复的错误，可以在这里处理
                    if error_code in [10013, 10014]:  # 示例错误码，根据实际情况调整
                        # 可以在这里添加token刷新逻辑
                        print(f"API授权错误({error_code}): {error_msg}，尝试刷新token")
                        # 刷新token的代码...
                        retry_count += 1
                        time.sleep(retry_delay)
                        retry_delay *= 2
                        continue
                        
                    raise PlatformRunError(f"API错误 ({error_code}): {error_msg}")
                
                return data.get("data", {})
                
            except requests.exceptions.Timeout:
                print(f"API请求超时(第{retry_count+1}次)，{retry_delay}秒后重试...")
                retry_count += 1
                time.sleep(retry_delay)
                retry_delay *= 2
            except requests.exceptions.ConnectionError as e:
                print(f"API连接错误(第{retry_count+1}次): {str(e)}，{retry_delay}秒后重试...")
                retry_count += 1
                time.sleep(retry_delay)
                retry_delay *= 2
            except Exception as e:
                if retry_count < max_retries - 1:
                    print(f"API请求失败(第{retry_count+1}次): {str(e)}，{retry_delay}秒后重试...")
                    retry_count += 1
                    time.sleep(retry_delay)
                    retry_delay *= 2
                else:
                    raise APIConnectionError(f"API请求失败: {str(e)}")
                    
        raise APIConnectionError(f"API请求失败: 已达最大重试次数")
    
    def _calculate_sign(self, method: str, app_id: str, timestamp: str, version: str, app_secret: str) -> str:
        """
        计算API签名
        
        Args:
            method: API方法名
            app_id: 应用ID
            timestamp: 时间戳
            version: API版本
            app_secret: 应用密钥
        
        Returns:
            签名字符串
        """
        # 按照指定格式拼接参数
        param_str = f"{method}?appId={app_id}&timestamp={timestamp}&version={version}"
        
        # 拼接app_secret
        sign_str = f"{param_str}{app_secret}"
        
        # MD5加密
        md5 = hashlib.md5()
        md5.update(sign_str.encode('utf-8'))
        sign = md5.hexdigest()
        
        return sign 