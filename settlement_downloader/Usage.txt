from datetime import datetime
import logging
from settlement_downloader import SettlementDownloader, PlatformA, PlatformB

# 配置日志
logging.basicConfig(level=logging.INFO)

# 创建平台实例
platform_a = PlatformA()
platform_b = PlatformB()

# 创建下载器实例
downloader = SettlementDownloader(platforms=[platform_a, platform_b])

# 定义时间范围和保存路径
start_date = datetime(2023, 1, 1)
end_date = datetime(2023, 12, 31)
save_dir = "./settlements"

# 执行下载
downloader.download_settlements(
    start_date=start_date,
    end_date=end_date,
    save_dir=save_dir,
    poll_interval=2,  # 轮询间隔2秒
    max_attempts=5    # 最多尝试5次
)