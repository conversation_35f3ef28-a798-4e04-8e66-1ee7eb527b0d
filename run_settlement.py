from db_manager.base_mysql import MySQLDB

import logging
from settlement_downloader.downloader import SettlementDownloader
from settlement_downloader.platforms.xiaohongshu import Xiaohongshu
from settlement_downloader.platforms.taobao import Tao<PERSON>
from settlement_downloader.platforms.douyin import Douyin
from settlement_downloader.platforms.dewu import DeWu

# 配置日志

logging.basicConfig(level=logging.INFO)

# 初始化数据库管理器
# db = MySQLDB(
#     host="*************",
#     user="vinehoodev",
#     password="vinehoo123",
#     database="vh_orders",
# )
db = MySQLDB(
    host="rm-8vb8nfr498cxlyhduwo.mysql.zhangbei.rds.aliyuncs.com",
    user="vinehoodev",
    password="ziAJWCLwOVs29NbB",
    database="vh_orders",
)

# 创建下载器实例
downloader = SettlementDownloader(db, platforms=[
    Xiaohong<PERSON>(),
    Taobao(),
    # Douy<PERSON>()
    # DeWu()
])

# 执行下载
downloader.download_settlements(
    max_attempts=5,  # 最多尝试5次
)
